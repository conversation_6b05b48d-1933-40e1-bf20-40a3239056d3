package nl.hva;
/*

import java.util.Iterator;

public class ArrayExercise {
    //Begin by looking at the Java tutorial on Arrays:
    // https://docs.oracle.com/javase/tutorial/java/nutsandbolts/arrays.html
    public static void main(String []args){

        //TODO - declare an array to hold 10 int values


        //An array has a property - its length.  This is fixed once the array has been declared
        //TODO - print out the length of the array declared above


        //values can be added to the array
        //TODO - assign the value of 0 to the first position in the array


        //TODO - print out the value with a suitable message


        //It is possible to iterate over the array to add, change or get elements
        for (int i = 0; i < row.length; i++){
            row[i] = i*i;
        }
        for (int i = 0; i < row.length; i++){
            System.out.println("The element in position "+i+ " is "+ row[i]);
        }

        //TODO: write code to print the array in reverse


        //There is a more concise method to iterate over arrays:  This is sometimes called a "for each".

        for (int i: row){
            System.out.println("i retrieved with for each method: "+ i);
        }

        //An array of objects can be created

        String [] days = new String[7];

        //TODO: write code to fill the array with the names of the days of the week.
        //TODO: write code to display the days.

    }
}
*/
