package nl.hva.shape;

/**
 * Simple circle class.
 */

import java.awt.*;

//<PERSON><PERSON> write the class definition for a Circle.  It should also have an instance field for radius


    /**
     * Create a circle with radius r
     *
     * @param c the colour of this circle
     * @param r the radius of this circle as a positive real number
     */

    //TODO - write the constructor

    /**
     * get the area of this circle as a double
     *
     * @return area
     */
    //TODO - override the getArea method

    /**
     * Get the radius as a double
     *
     * @return the radius
     */
    //TODO - write the required method

    /**
     * @param radius the size of this circle as a positive real number.
     */
    //TODO - write a method to set the radius


    /**
     * Two circles are equal if their radii are equal.
     * This is the overridden version of the method inherited from Object
     * @param o - another shapes.Circle
     */
    //TODO - override the equals method

    /**
     * Returns an int which is a hashcode for this object.
     * This is the overridden version of the method inherited from Object and the documentation states:
     * "If two objects are equal according to the equals(Object) method,
     * then calling the hashCode method on each of the two objects must produce the same integer result" so
     * the hashcode is generated from the radius. See the documentation in the Java Object class
     */
    //TODO - override the hashCode method (hint:  use Double.valueOf(radius).hashCode()+ super.hashCode() )




