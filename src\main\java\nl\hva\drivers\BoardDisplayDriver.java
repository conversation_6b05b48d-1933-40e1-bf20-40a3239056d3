package nl.hva.drivers;
/*

import nl.hva.board.Board;
import nl.hva.board.Row;
import nl.hva.shape.Circle;
import nl.hva.shape.Rectangle;
import nl.hva.shape.Shape;
import nl.hva.ui.BoardDisplayer;


import java.awt.*;

public class BoardDisplayDriver {
    public static void main (String args[]){
       //Display a Board containing shapes
        Board<Shape> board = new Board<>(4);
        board.add(new Circle(Color.BLUE, 50), 0, 0);
        board.add(new Circle(Color.GREEN, 80), 1, 0);
        board.add(new Circle(Color.RED, 100), 2, 0);
        board.add(new Circle(Color.YELLOW, 90), 3, 0);
        board.add(new Rectangle((Color.CYAN), 80, 60), 0, 1);
        board.add(new Circle(Color.BLUE, 70), 0, 2);
        board.add(new Circle(Color.GREEN, 80), 0, 3);
        board.add(new Circle(Color.RED, 100), 3, 1);
        board.add(new Circle(Color.YELLOW, 110), 3, 2);
        board.add(new Rectangle((Color.CYAN), 80, 80), 3, 3);
        System.out.println(board);

        BoardDisplayer displayer = new BoardDisplayer(board);

    }
}
*/
