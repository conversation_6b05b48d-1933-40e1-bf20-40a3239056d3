package nl.hva.drivers;
/*

import nl.hva.shape.Circle;
import nl.hva.shape.Rectangle;
import nl.hva.ui.ShapeDisplayer;

import java.awt.*;

public class ShapeDisplayDriver {

    public static void main(String args[]) {

        //Display a single Circle:
        //TODO Create an instance of the Circle. Use quite a large radius (at least 40).

        //TODO Create an instance of the ShapeDisplayer
        // with the circle as a parameter to the constructor.

        Circle circle = new Circle(Color.BLUE, 100);
        ShapeDisplayer displayer = new ShapeDisplayer(circle);


        //TODO Display a single Rectangle:
        //TODO Use the ShapeDisplayer to display as you did with the Circle.

       // Rectangle rectangle = new Rectangle(Color.CYAN, 400, 600);
       // ShapeDisplayer displayerisplayer = new ShapeDisplayer(rectangle);
    }

}
*/
