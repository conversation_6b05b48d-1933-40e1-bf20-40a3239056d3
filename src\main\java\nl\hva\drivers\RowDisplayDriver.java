package nl.hva.drivers;
/*

import nl.hva.board.Board;
import nl.hva.board.Row;
import nl.hva.shape.Circle;
import nl.hva.shape.Rectangle;
import nl.hva.shape.Shape;
import nl.hva.ui.RowDisplayer;
import nl.hva.ui.ShapeDisplayer;

import java.awt.*;

public class RowDisplayDriver {

    public static void main (String args[]){
        //TODO Instantiate a Row of size 5 and add rectangles and circles.  You can also leave gaps in the row.
        //TODO Create an instance of the ShapeDisplayer with the row as a parameter.
        Row<Shape> row = new Row<>(5);
        row.add (new Circle(Color.BLUE,80), 0);
        row.add (new Circle(Color.GREEN,100), 1);
        row.add (new Circle(Color.YELLOW,75), 2);
        row.add (new Rectangle((Color.CYAN), 100, 45),4);
        System.out.println(row);
        RowDisplayer rowDisplayer = new RowDisplayer(row);

    }
}
*/
